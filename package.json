{"name": "navhaus-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:analyze": "cross-env ANALYZE=true next build", "export": "next build && next export", "type-check": "tsc --noEmit", "build:production": "npm run lint && npm run type-check && npm run build", "generate-favicons": "node scripts/generate-favicons.js"}, "dependencies": {"next": "14.2.15", "react": "^18", "react-dom": "^18", "gray-matter": "^4.0.3", "marked": "^12.0.0", "highlight.js": "^11.9.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "sharp": "^0.34.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}