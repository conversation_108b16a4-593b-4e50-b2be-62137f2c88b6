'use client'

interface SchemaMarkupProps {
  type: 'organization' | 'localBusiness' | 'service' | 'webpage'
  data?: any
}

export default function SchemaMarkup({ type, data = {} }: SchemaMarkupProps) {
  const getSchemaData = () => {
    const baseUrl = 'https://navhaus.com'
    
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Navhaus",
          "alternateName": "Navhaus Agency",
          "description": "WordPress agency specializing in custom WordPress websites and web development. We build fast, clean, and scalable WordPress sites and web apps for businesses that value quality over quantity.",
          "url": baseUrl,
          "logo": `${baseUrl}/images/logo.png`,
          "image": `${baseUrl}/images/og-image.png`,
          "sameAs": [
            "https://github.com/navhaus",
            // Add other social media profiles when available
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "url": `${baseUrl}/contact`,
            "availableLanguage": "English"
          },
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "US"
          },
          "foundingDate": "2024",
          "founder": {
            "@type": "Person",
            "name": "Navhaus Team"
          },
          "knowsAbout": [
            "WordPress Development",
            "Custom WordPress Websites",
            "Web Development",
            "React Development",
            "Next.js Development",
            "Web Design",
            "Performance Optimization",
            "SEO"
          ],
          "serviceArea": {
            "@type": "Place",
            "name": "Worldwide"
          }
        }

      case 'localBusiness':
        return {
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "@id": `${baseUrl}/#business`,
          "name": "Navhaus",
          "description": "WordPress agency specializing in custom WordPress websites and web development. We build fast, clean, and scalable WordPress sites and web apps for businesses that value quality over quantity.",
          "url": baseUrl,
          "telephone": "+1-XXX-XXX-XXXX", // Update with actual phone number
          "email": "<EMAIL>", // Update with actual email
          "logo": `${baseUrl}/images/logo.png`,
          "image": `${baseUrl}/images/og-image.png`,
          "priceRange": "$$",
          "currenciesAccepted": "USD",
          "paymentAccepted": "Cash, Credit Card, Bank Transfer",
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "US"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": "40.7128",
            "longitude": "-74.0060"
          },
          "openingHours": "Mo-Fr 09:00-17:00",
          "serviceArea": {
            "@type": "Place",
            "name": "Worldwide"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Web Development Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Custom WordPress Development",
                  "description": "Custom WordPress websites built from scratch with modern development practices."
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Web Application Development",
                  "description": "Custom web applications built with React, Next.js, and modern technologies."
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "WordPress Performance Optimization",
                  "description": "Speed optimization and performance improvements for WordPress websites."
                }
              }
            ]
          }
        }

      case 'service':
        return {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": data.name || "WordPress Development Services",
          "description": data.description || "Professional WordPress development services including custom themes, plugins, and performance optimization.",
          "provider": {
            "@type": "Organization",
            "name": "Navhaus",
            "url": baseUrl
          },
          "areaServed": {
            "@type": "Place",
            "name": "Worldwide"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "WordPress Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Custom WordPress Websites",
                  "description": "Bespoke WordPress sites built from the ground up with clean, custom code."
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "WordPress Theme Development",
                  "description": "Custom WordPress themes built with modern tools and best practices."
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "WordPress Performance Optimization",
                  "description": "Speed optimization, caching strategies, and performance audits for WordPress sites."
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "WordPress Maintenance",
                  "description": "Ongoing support, security updates, and maintenance for WordPress websites."
                }
              }
            ]
          },
          "category": "Web Development",
          "serviceType": "WordPress Development"
        }

      case 'webpage':
        return {
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": data.title || "Navhaus | Custom WordPress Website Development Agency",
          "description": data.description || "WordPress agency specializing in custom WordPress websites and web development. We build fast, clean, and scalable WordPress sites and web apps.",
          "url": data.url || baseUrl,
          "inLanguage": "en-US",
          "isPartOf": {
            "@type": "WebSite",
            "name": "Navhaus",
            "url": baseUrl
          },
          "about": {
            "@type": "Organization",
            "name": "Navhaus"
          },
          "mainEntity": {
            "@type": "Organization",
            "name": "Navhaus",
            "description": "WordPress agency specializing in custom WordPress websites and web development."
          },
          "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": data.breadcrumbs || [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": baseUrl
              }
            ]
          }
        }

      default:
        return {}
    }
  }

  const schemaData = getSchemaData()

  if (!schemaData || Object.keys(schemaData).length === 0) {
    return null
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schemaData)
      }}
    />
  )
}
