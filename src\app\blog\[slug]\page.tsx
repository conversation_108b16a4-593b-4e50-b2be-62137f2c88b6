import type { Metadata } from 'next'
import { notFound } from 'next/navigation'
import <PERSON>Wrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import SchemaMarkup from '@/components/seo/SchemaMarkup'
import { getAllPosts, getPostBySlug, getRelatedPosts } from '@/lib/mdx'
import MDXContent from '@/components/blog/MDXContent'
import ReadingProgress from '@/components/blog/ReadingProgress'



interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found | Navhaus Blog'
    }
  }

  return {
    title: post.seo.title,
    description: post.seo.description,
    keywords: post.seo.keywords,
    openGraph: {
      title: post.seo.title,
      description: post.seo.description,
      url: `https://navhaus.com/blog/${post.slug}`,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seo.title,
      description: post.seo.description,
    }
  }
}

export async function generateStaticParams() {
  const posts = getAllPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export default function BlogPost({ params }: BlogPostPageProps) {
  const post = getPostBySlug(params.slug)

  if (!post) {
    notFound()
  }

  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": "https://navhaus.com/images/og-image.png",
    "author": {
      "@type": "Organization",
      "name": post.author,
      "url": "https://navhaus.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Navhaus",
      "logo": {
        "@type": "ImageObject",
        "url": "https://navhaus.com/images/logo.png"
      }
    },
    "datePublished": post.date,
    "dateModified": post.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://navhaus.com/blog/${post.slug}`
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema)
        }}
      />
      
      <ReadingProgress />

      <PageWrapper>
        {/* Article Header */}
        <article className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background">
          <div className="max-w-5xl mx-auto">
                {/* Breadcrumbs */}
                <nav className="mb-12">
                  <ol className="flex items-center space-x-3 text-sm text-bauhaus-black opacity-60">
                    <li><Link href="/" className="hover:text-bauhaus-blue transition-colors">Home</Link></li>
                    <li className="opacity-40">→</li>
                    <li><Link href="/blog" className="hover:text-bauhaus-blue transition-colors">Blog</Link></li>
                    <li className="opacity-40">→</li>
                    <li className="font-medium opacity-80">{post.title}</li>
                  </ol>
                </nav>

                {/* Article Header */}
                <header className="mb-16 text-center">
                  <div className="flex items-center justify-center space-x-6 text-sm text-bauhaus-black opacity-60 mb-8">
                    <span className="bg-bauhaus-blue text-bauhaus-white px-4 py-2 rounded-full text-xs font-bold uppercase tracking-wide">
                      {post.category}
                    </span>
                    <span className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <span>{post.readTime}</span>
                    </span>
                    <time className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      <span>
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </time>
                  </div>

                  <h1 className="text-5xl md:text-6xl font-bold leading-tight mb-8 text-bauhaus-black tracking-tight">
                    {post.title}
                  </h1>

                  <p className="text-2xl text-bauhaus-black opacity-70 leading-relaxed max-w-3xl mx-auto font-light">
                    {post.excerpt}
                  </p>
                </header>

                {/* Article Content */}
                <div className="bg-bauhaus-white rounded-3xl p-8 md:p-12 shadow-sm">
                  <MDXContent content={post.content} />
                </div>

                {/* Article Footer */}
                <footer className="mt-16 pt-12 border-t border-bauhaus-black border-opacity-20">
                  <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0">
                    <div className="text-center md:text-left">
                      <p className="text-sm text-bauhaus-black opacity-60 mb-2">Written by</p>
                      <p className="font-bold text-bauhaus-black text-lg">{post.author}</p>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                      <Link
                        href="/blog"
                        className="btn-primary"
                      >
                        ← Back to Blog
                      </Link>
                      <Link
                        href="/contact"
                        className="btn-red"
                      >
                        Start Your Project
                      </Link>
                    </div>
                  </div>
                </footer>
          </div>
        </article>

        {/* Related Posts */}
        <section className="hidden px-6 md:px-12 lg:px-24 py-16 bg-brand-background">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-2xl font-bold mb-8">Related Articles</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {getRelatedPosts(post.slug, post.category, 2)
                .map((relatedPost) => (
                  <article key={relatedPost.slug} className="bg-white rounded-3xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                          {relatedPost.category}
                        </span>
                        <span>{relatedPost.readTime}</span>
                      </div>
                      
                      <h3 className="text-lg font-bold text-gray-900 leading-tight">
                        <Link href={`/blog/${relatedPost.slug}`} className="hover:text-bauhaus-blue transition-colors">
                          {relatedPost.title}
                        </Link>
                      </h3>
                      
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {relatedPost.excerpt}
                      </p>
                      
                      <Link 
                        href={`/blog/${relatedPost.slug}`}
                        className="text-bauhaus-blue font-medium hover:text-bauhaus-red transition-colors text-sm"
                      >
                        Read more →
                      </Link>
                    </div>
                  </article>
                ))}
            </div>
          </div>
        </section>
      </PageWrapper>
    </>
  )
}
