import type { Metadata } from 'next'
import { notFound } from 'next/navigation'
import <PERSON>Wrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import SchemaMarkup from '@/components/seo/SchemaMarkup'
import { getAllPosts, getPostBySlug, getRelatedPosts } from '@/lib/mdx'
import MDXContent from '@/components/blog/MDXContent'



interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found | Navhaus Blog'
    }
  }

  return {
    title: post.seo.title,
    description: post.seo.description,
    keywords: post.seo.keywords,
    openGraph: {
      title: post.seo.title,
      description: post.seo.description,
      url: `https://navhaus.com/blog/${post.slug}`,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seo.title,
      description: post.seo.description,
    }
  }
}

export async function generateStaticParams() {
  const posts = getAllPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export default function BlogPost({ params }: BlogPostPageProps) {
  const post = getPostBySlug(params.slug)

  if (!post) {
    notFound()
  }

  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": "https://navhaus.com/images/og-image.png",
    "author": {
      "@type": "Organization",
      "name": post.author,
      "url": "https://navhaus.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Navhaus",
      "logo": {
        "@type": "ImageObject",
        "url": "https://navhaus.com/images/logo.png"
      }
    },
    "datePublished": post.date,
    "dateModified": post.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://navhaus.com/blog/${post.slug}`
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema)
        }}
      />
      
      <PageWrapper>
        {/* Article Header */}
        <article className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <nav className="mb-8">
              <ol className="flex items-center space-x-2 text-sm text-gray-500">
                <li><Link href="/" className="hover:text-bauhaus-blue">Home</Link></li>
                <li>→</li>
                <li><Link href="/blog" className="hover:text-bauhaus-blue">Blog</Link></li>
                <li>→</li>
                <li className="text-gray-700">{post.title}</li>
              </ol>
            </nav>

            {/* Article Meta */}
            <div className="mb-8">
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <span className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                  {post.category}
                </span>
                <span>{post.readTime}</span>
                <time>
                  {new Date(post.date).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </time>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-4">
                {post.title}
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed">
                {post.excerpt}
              </p>
            </div>

            {/* Article Content */}
            <MDXContent content={post.content} />

            {/* Article Footer */}
            <div className="mt-16 pt-8 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 mb-2">Written by</p>
                  <p className="font-medium text-gray-900">{post.author}</p>
                </div>
                
                <Link 
                  href="/contact"
                  className="btn-red"
                >
                  Start Your Project
                </Link>
              </div>
            </div>
          </div>
        </article>

        {/* Related Posts */}
        <section className="px-6 md:px-12 lg:px-24 py-16 bg-brand-background">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-2xl font-bold mb-8">Related Articles</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {getRelatedPosts(post.slug, post.category, 2)
                .map((relatedPost) => (
                  <article key={relatedPost.slug} className="bg-white rounded-3xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                          {relatedPost.category}
                        </span>
                        <span>{relatedPost.readTime}</span>
                      </div>
                      
                      <h3 className="text-lg font-bold text-gray-900 leading-tight">
                        <Link href={`/blog/${relatedPost.slug}`} className="hover:text-bauhaus-blue transition-colors">
                          {relatedPost.title}
                        </Link>
                      </h3>
                      
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {relatedPost.excerpt}
                      </p>
                      
                      <Link 
                        href={`/blog/${relatedPost.slug}`}
                        className="text-bauhaus-blue font-medium hover:text-bauhaus-red transition-colors text-sm"
                      >
                        Read more →
                      </Link>
                    </div>
                  </article>
                ))}
            </div>
          </div>
        </section>
      </PageWrapper>
    </>
  )
}
