import type { Metadata } from 'next'
import PageWrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'
import { getAllPosts, getFeaturedPosts } from '@/lib/mdx'

export const metadata: Metadata = {
  title: 'WordPress & Web Development Blog | Navhaus Insights',
  description: 'Expert insights on WordPress development, web development best practices, performance optimization, and modern web technologies from the Navhaus team.',
  keywords: [
    'wordpress blog',
    'web development blog',
    'wordpress development tips',
    'web development insights',
    'wordpress performance',
    'web development best practices',
    'wordpress tutorials',
    'web development guides',
    'navhaus blog'
  ],
  openGraph: {
    title: 'WordPress & Web Development Blog | Navhaus Insights',
    description: 'Expert insights on WordPress development, web development best practices, performance optimization, and modern web technologies from the Navhaus team.',
    url: 'https://navhaus.com/blog',
  },
}

export default function Blog() {
  const allPosts = getAllPosts()
  const featuredPosts = getFeaturedPosts()
  const regularPosts = allPosts.filter(post => !post.featured)

  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="animated-section relative px-6 py-6 md:px-12 lg:px-24 lg:py-0 overflow-hidden flex items-center lg:h-[calc(100vh-6.5rem)]">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-none">
                WordPress & Web Development Insights
              </h1>
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 max-w-lg">
                Expert insights, tutorials, and best practices for WordPress development, 
                web performance, and modern web technologies.
              </p>
            </div>

            <div className="relative h-96 lg:h-[400px]">
              <div className="absolute inset-0">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
              </div>
              <div className="relative flex justify-center items-center h-full">
                <AnimatedSoftCircle size="xl" color="yellow" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
              </div>
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="blue" className="w-16 h-8" animationPreset="flowing" animationIndex={2} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Featured Articles</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              In-depth guides and insights from our WordPress and web development experts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <article key={post.slug} className="bg-white rounded-3xl p-8 shadow-sm hover:shadow-md transition-shadow">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                      {post.category}
                    </span>
                    <span>{post.readTime}</span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 leading-tight">
                    <Link href={`/blog/${post.slug}`} className="hover:text-bauhaus-blue transition-colors">
                      {post.title}
                    </Link>
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between pt-4">
                    <time className="text-sm text-gray-500">
                      {new Date(post.date).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </time>
                    <Link 
                      href={`/blog/${post.slug}`}
                      className="text-bauhaus-blue font-medium hover:text-bauhaus-red transition-colors"
                    >
                      Read more →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* All Posts */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Latest Articles</h2>
          </div>

          <div className="space-y-8">
            {regularPosts.map((post) => (
              <article key={post.slug} className="bg-gray-900 rounded-3xl p-8 hover:bg-gray-800 transition-colors">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
                  <div className="md:col-span-3 space-y-4">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="bg-bauhaus-yellow text-bauhaus-black px-3 py-1 rounded-full text-xs font-medium">
                        {post.category}
                      </span>
                      <span className="text-gray-400">{post.readTime}</span>
                      <time className="text-gray-400">
                        {new Date(post.date).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </time>
                    </div>
                    
                    <h3 className="text-xl font-bold leading-tight">
                      <Link href={`/blog/${post.slug}`} className="hover:text-bauhaus-yellow transition-colors">
                        {post.title}
                      </Link>
                    </h3>
                    
                    <p className="text-gray-300 leading-relaxed">
                      {post.excerpt}
                    </p>
                  </div>
                  
                  <div className="md:col-span-1 text-right">
                    <Link 
                      href={`/blog/${post.slug}`}
                      className="inline-block bg-bauhaus-yellow text-bauhaus-black px-6 py-3 rounded-full font-medium hover:bg-brand-background transition-colors"
                    >
                      Read Article
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-brand-background overflow-hidden">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-display font-bold mb-8">Stay Updated</h2>
          <p className="text-xl text-gray-700 leading-relaxed mb-12 max-w-2xl mx-auto">
            Get the latest WordPress and web development insights delivered to your inbox.
          </p>
          
          <div className="max-w-md mx-auto">
            <div className="flex gap-4">
              <input
                type="email"
                placeholder="<EMAIL>"
                className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-xl focus:border-bauhaus-blue focus:outline-none"
              />
              <button className="btn-red whitespace-nowrap">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
